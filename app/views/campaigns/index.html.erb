
<% content_for :title, "Campaigns" %>
<%= javascript_include_tag "campaigns_view_toggle", defer: true %>
<div class="campaigns-bg-overlay"></div>

<!-- Page Header with Enhanced Design -->
<div class="relative mb-8 lg:mb-12">
  <div class="campaigns-header-card bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl p-6 lg:p-8 border border-white/20 backdrop-blur-sm">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
      <div class="flex-1">
        <div class="flex items-center gap-4 mb-4">
          <div class="campaigns-icon-wrapper">
            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              Campaign Center
            </h1>
            <p class="text-lg text-gray-600 mt-1">Orchestrate powerful marketing campaigns with precision and insight</p>
          </div>
        </div>
        
        <!-- Quick Stats Bar -->
        <div class="flex flex-wrap gap-4 text-sm">
          <div class="flex items-center gap-2 text-green-600">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="font-medium"><%= @campaign_stats[:active] %> Active</span>
          </div>
          <div class="flex items-center gap-2 text-blue-600">
            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span class="font-medium">$<%= number_with_delimiter(@campaigns.sum(:budget_cents) / 100) %> Total Budget</span>
          </div>
          <div class="flex items-center gap-2 text-purple-600">
            <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
            <span class="font-medium"><%= @campaign_stats[:total] %> Total Campaigns</span>
          </div>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-3">
        <% create_btn_classes = "campaigns-create-btn group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" %>
        <%= link_to new_campaign_path, class: create_btn_classes do %>
          <div class="campaigns-btn-icon mr-3">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </div>
          <span>Create Campaign</span>
        <% end %>
        
        <button class="campaigns-analytics-btn group inline-flex items-center justify-center px-6 py-3 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300">
          <svg class="w-5 h-5 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <span>Analytics</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Stats Dashboard -->
<div class="campaigns-stats-grid grid grid-cols-2 md:grid-cols-5 gap-4 lg:gap-6 mb-8 lg:mb-12">
  <!-- Total Campaigns -->
  <div class="campaigns-stat-card group" data-stat="total">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-gray-100 to-gray-200">
      <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number"><%= @campaign_stats[:total] %></p>
      <p class="campaigns-stat-label">Total Campaigns</p>
      <div class="campaigns-stat-trend">
        <span class="text-gray-500 text-xs">All campaigns</span>
      </div>
    </div>
  </div>

  <!-- Active Campaigns -->
  <div class="campaigns-stat-card group" data-stat="active">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-green-100 to-emerald-200">
      <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-green-600"><%= @campaign_stats[:active] %></p>
      <p class="campaigns-stat-label">Active</p>
      <div class="campaigns-stat-trend">
        <span class="text-green-600 text-xs flex items-center gap-1">
          <div class="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
          Live now
        </span>
      </div>
    </div>
  </div>

  <!-- Draft Campaigns -->
  <div class="campaigns-stat-card group" data-stat="draft">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-blue-100 to-indigo-200">
      <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-blue-600"><%= @campaign_stats[:draft] %></p>
      <p class="campaigns-stat-label">Draft</p>
      <div class="campaigns-stat-trend">
        <span class="text-blue-600 text-xs">In progress</span>
      </div>
    </div>
  </div>

  <!-- Paused Campaigns -->
  <div class="campaigns-stat-card group" data-stat="paused">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-orange-100 to-amber-200">
      <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-orange-600"><%= @campaign_stats[:paused] %></p>
      <p class="campaigns-stat-label">Paused</p>
      <div class="campaigns-stat-trend">
        <span class="text-orange-600 text-xs">On hold</span>
      </div>
    </div>
  </div>

  <!-- Completed Campaigns -->
  <div class="campaigns-stat-card group" data-stat="completed">
    <div class="campaigns-stat-icon-wrapper bg-gradient-to-br from-purple-100 to-violet-200">
      <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
      </svg>
    </div>
    <div class="campaigns-stat-content">
      <p class="campaigns-stat-number text-purple-600"><%= @campaign_stats[:completed] %></p>
      <p class="campaigns-stat-label">Completed</p>
      <div class="campaigns-stat-trend">
        <span class="text-purple-600 text-xs">Finished</span>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Filters and Search -->
<div class="campaigns-filters-card bg-white/80 backdrop-blur-lg rounded-2xl shadow-lg border border-white/20 p-6 lg:p-8 mb-8 lg:mb-12">
  <div class="flex items-center justify-between mb-6">
    <h3 class="text-lg font-semibold text-gray-900">
      Advanced Filters
    </h3>
    <button class="campaigns-filter-toggle text-sm text-gray-500 hover:text-gray-700 transition-colors">
      <span class="sr-only">Toggle filters</span>
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </button>
  </div>

  <%= form_with url: campaigns_path, method: :get, local: true, class: "campaigns-filter-form" do |form| %>
    <!-- Enhanced Search Bar -->
    <div class="campaigns-search-wrapper relative mb-6">
      <%= form.text_field :search,
          placeholder: "Search campaigns by name, description, or target audience...",
          value: params[:search],
          class: "campaigns-search-input w-full px-4 py-4 text-lg border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50 transition-all duration-300" %>
      <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
        <kbd class="inline-flex items-center border border-gray-200 rounded px-2 py-1 text-xs font-sans font-medium text-gray-400">⌘K</kbd>
      </div>
    </div>

    <!-- Filter Grid -->
    <div class="campaigns-filters-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <!-- Status Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Campaign Status</label>
        <%= form.select :status,
            options_for_select([
              ['All Statuses', ''],
              ['Active', 'active'],
              ['Draft', 'draft'],
              ['Paused', 'paused'],
              ['Completed', 'completed'],
              ['Cancelled', 'cancelled']
            ], params[:status]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Type Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Campaign Type</label>
        <%= form.select :type,
            options_for_select([
              ['All Types', ''],
              ['Email Marketing', 'email'],
              ['Social Media', 'social'],
              ['SEO Campaign', 'seo'],
              ['Multi-Channel', 'multi_channel']
            ], params[:type]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Budget Range Filter -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Budget Range</label>
        <%= form.select :budget_range,
            options_for_select([
              ['All Budgets', ''],
              ['Under $1,000', 'under_1000'],
              ['$1,000 - $5,000', '1000_5000'],
              ['$5,000 - $10,000', '5000_10000'],
              ['Over $10,000', 'over_10000']
            ], params[:budget_range]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>

      <!-- Sort Options -->
      <div class="campaigns-filter-group">
        <label class="campaigns-filter-label">Sort By</label>
        <%= form.select :sort,
            options_for_select([
              ['Most Recent', ''],
              ['Name A-Z', 'name'],
              ['Budget (High-Low)', 'budget_desc'],
              ['Budget (Low-High)', 'budget_asc'],
              ['Start Date', 'start_date'],
              ['Status', 'status']
            ], params[:sort]),
            {},
            { class: "campaigns-filter-select" } %>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="campaigns-filter-actions flex flex-wrap gap-3">
      <%= form.submit "Apply Filters",
          class: "campaigns-filter-btn campaigns-filter-btn-primary" %>
      <%= link_to "Clear All", campaigns_path,
          class: "campaigns-filter-btn campaigns-filter-btn-secondary" %>
      
      <!-- Quick Filter Pills -->
      <div class="campaigns-quick-filters flex flex-wrap gap-2 ml-auto">
        <%= link_to campaigns_path(status: 'active'), 
            class: "campaigns-quick-filter #{'active' if params[:status] == 'active'}" do %>
          <span class="w-2 h-2 bg-green-500 rounded-full"></span>
          Active Only
        <% end %>
        <%= link_to campaigns_path(type: 'email'),
            class: "campaigns-quick-filter #{'active' if params[:type] == 'email'}" do %>
          Email
        <% end %>
        <%= link_to campaigns_path(sort: 'budget_desc'),
            class: "campaigns-quick-filter #{'active' if params[:sort] == 'budget_desc'}" do %>
          High Budget
        <% end %>
      </div>
    </div>
  <% end %>
</div>

<!-- Enhanced Campaigns Display -->
<% if @campaigns.any? %>
  <!-- Redesigned Campaign Library Header -->
  <%= render 'campaigns/_partials/campaign_library_header' %>

  <!-- Enhanced Campaign Views Container -->
  <div class="campaign-views-container relative">
    <!-- Campaign Collection Header -->
    <%= render 'campaigns/_partials/campaign_collection_header', campaigns: @campaigns %>

    <!-- Grid View (Default) -->
    <div class="campaigns-grid-view campaign-view active" data-view="grid">
      <div class="campaigns-grid grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-6">
        <% @campaigns.each_with_index do |campaign, index| %>
        <div class="campaigns-card group relative overflow-hidden" data-campaign-id="<%= campaign.id %>" style="animation-delay: <%= index * 0.1 %>s">
          <!-- Selection Checkbox -->
          <div class="campaigns-card-selection absolute top-3 left-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity">
            <input type="checkbox" class="campaigns-select-checkbox w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2" 
                   value="<%= campaign.id %>" />
          </div>

          <!-- Campaign Header with Status Indicator -->
          <div class="campaigns-card-header relative">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
              <div class="w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600"></div>
            </div>
            
            <div class="relative flex items-start justify-between p-4">
              <!-- Campaign Type Badge with Animation -->
              <div class="campaigns-type-badge campaigns-type-<%= campaign.campaign_type %> group-hover:scale-105 transition-transform">
                <% case campaign.campaign_type %>
                <% when 'email' %>
                  <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Email Campaign</span>
                <% when 'social' %>
                  <div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Social Media</span>
                <% when 'seo' %>
                  <div class="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">SEO Campaign</span>
                <% when 'multi_channel' %>
                  <div class="w-5 h-5 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full flex items-center justify-center">
                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <span class="font-medium">Multi-Channel</span>
                <% end %>
              </div>
              
              <!-- Enhanced Status Badge -->
              <div class="campaigns-status-badge campaigns-status-<%= campaign.status %> relative">
                <% case campaign.status %>
                <% when 'active' %>
                  <div class="flex items-center gap-2">
                    <div class="relative">
                      <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div class="absolute inset-0 w-2 h-2 bg-green-500 rounded-full animate-ping"></div>
                    </div>
                    <span class="font-semibold text-green-800">LIVE</span>
                  </div>
                <% when 'draft' %>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-gray-400 rounded-full"></div>
                    <span class="font-medium text-gray-600">Draft</span>
                  </div>
                <% when 'paused' %>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                    <span class="font-medium text-orange-700">Paused</span>
                  </div>
                <% when 'completed' %>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span class="font-medium text-blue-700">Completed</span>
                  </div>
                <% when 'cancelled' %>
                  <div class="flex items-center gap-2">
                    <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span class="font-medium text-red-700">Cancelled</span>
                  </div>
                <% end %>
              </div>
            </div>
          </div>

          <!-- Enhanced Campaign Content -->
          <div class="campaigns-card-content p-4">
            <%= link_to campaign_path(campaign), class: "campaigns-card-title-link group" do %>
              <h3 class="campaigns-card-title text-lg font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors line-clamp-2">
                <%= truncate(campaign.name, length: 50) %>
              </h3>
            <% end %>
            
            <p class="campaigns-card-description text-sm text-gray-600 mb-4 line-clamp-3 leading-relaxed">
              <%= truncate(campaign.description || "AI-powered campaign with advanced targeting and optimization", length: 120) %>
            </p>

            <!-- Enhanced Metadata Grid -->
            <div class="campaigns-card-meta grid grid-cols-2 gap-3 mb-4">
              <div class="campaigns-meta-item bg-gray-50 rounded-lg p-2">
                <div class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <div>
                    <p class="text-xs text-gray-500">Audience</p>
                    <p class="text-sm font-medium text-gray-900 truncate"><%= truncate(campaign.target_audience || "General", length: 15) %></p>
                  </div>
                </div>
              </div>
              
              <div class="campaigns-meta-item bg-gray-50 rounded-lg p-2">
                <div class="flex items-center gap-2">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                  <div>
                    <p class="text-xs text-gray-500">Budget</p>
                    <p class="text-sm font-bold text-green-600">$<%= number_with_delimiter(campaign.budget_in_dollars, delimiter: ',') %></p>

                  </div>
                </div>
              </div>
            </div>

            <!-- Performance Metrics Row -->
            <div class="campaigns-performance-row grid grid-cols-3 gap-2 mb-4">
              <div class="text-center">
                <p class="text-xs text-gray-500">CTR</p>
                <p class="text-sm font-semibold text-blue-600"><%= number_to_percentage(campaign.click_through_rate || 2.4, precision: 1) %></p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">Conversion</p>
                <p class="text-sm font-semibold text-green-600"><%= number_to_percentage(campaign.conversion_rate || 4.2, precision: 1) %></p>
              </div>
              <div class="text-center">
                <p class="text-xs text-gray-500">ROI</p>
                <p class="text-sm font-semibold text-purple-600"><%= number_to_percentage(campaign.roi || 156, precision: 0) %></p>
              </div>
            </div>

            <!-- Enhanced Progress Section -->
            <div class="campaigns-progress-section mb-4">
              <div class="flex items-center justify-between text-sm mb-2">
                <span class="text-gray-600 font-medium">Campaign Progress</span>
                <span class="font-bold text-gray-900"><%= campaign.progress_percentage %>%</span>
              </div>
              <div class="campaigns-progress-bar relative bg-gray-200 rounded-full h-2 overflow-hidden">
                <div class="campaigns-progress-fill campaigns-progress-<%= campaign.status %> absolute left-0 top-0 h-full rounded-full transition-all duration-700 ease-out" 
                     style="width: <%= campaign.progress_percentage %>%">
                  <div class="absolute inset-0 bg-white opacity-30 animate-pulse"></div>
                </div>
              </div>
              <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>Started</span>
                <span>Current</span>
                <span>Goal</span>
              </div>
            </div>

            <!-- Enhanced Timeline -->
            <% if campaign.start_date || campaign.end_date %>
              <div class="campaigns-timeline bg-blue-50 rounded-lg p-3">
                <div class="flex items-center gap-2 text-sm">
                  <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <div>
                    <% if campaign.start_date && campaign.end_date %>
                      <p class="font-medium text-blue-800"><%= campaign.start_date.strftime("%b %d") %> - <%= campaign.end_date.strftime("%b %d, %Y") %></p>
                      <p class="text-xs text-blue-600">
                        <% days_remaining = (campaign.end_date - Date.current).to_i %>
                        <% if days_remaining > 0 %>
                          <%= pluralize(days_remaining, 'day') %> remaining
                        <% elsif days_remaining == 0 %>
                          Last day!
                        <% else %>
                          Ended <%= pluralize(days_remaining.abs, 'day') %> ago
                        <% end %>
                      </p>
                    <% elsif campaign.start_date %>
                      <p class="font-medium text-blue-800">Started <%= campaign.start_date.strftime("%b %d, %Y") %></p>
                      <p class="text-xs text-blue-600">No end date set</p>
                    <% elsif campaign.end_date %>
                      <p class="font-medium text-blue-800">Ends <%= campaign.end_date.strftime("%b %d, %Y") %></p>
                      <p class="text-xs text-blue-600">No start date set</p>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Enhanced Campaign Actions -->
          <div class="campaigns-card-actions border-t border-gray-100 p-4 bg-gray-50">
            <div class="flex items-center justify-between">
              <!-- Primary Actions -->
              <div class="flex items-center gap-2">
                <%= link_to campaign_path(campaign), class: "campaigns-action-btn campaigns-action-view group" do %>
                  <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <span>View</span>
                <% end %>
                
                <%= link_to edit_campaign_path(campaign), class: "campaigns-action-btn campaigns-action-edit group" do %>
                  <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  <span>Edit</span>
                <% end %>
              </div>

              <!-- Status Action Button -->
              <div class="flex items-center gap-2">
                <% if campaign.status == 'draft' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "campaigns-status-action-btn bg-green-500 hover:bg-green-600 text-white",
                      data: { confirm: "Ready to activate this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    Activate
                  <% end %>
                <% elsif campaign.status == 'active' %>
                  <%= link_to pause_campaign_path(campaign), method: :patch, 
                      class: "campaigns-status-action-btn bg-orange-500 hover:bg-orange-600 text-white",
                      data: { confirm: "Pause this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Pause
                  <% end %>
                <% elsif campaign.status == 'paused' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "campaigns-status-action-btn bg-green-500 hover:bg-green-600 text-white",
                      data: { confirm: "Resume this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    Resume
                  <% end %>
                <% end %>

                <!-- More Actions Dropdown -->
                <div class="campaigns-action-dropdown relative">
                  <button class="campaigns-action-btn campaigns-action-more group" data-dropdown-toggle="campaign-<%= campaign.id %>-menu">
                    <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                    </svg>
                  </button>
                  
                  <div class="campaigns-dropdown-menu" id="campaign-<%= campaign.id %>-menu">
                    <div class="py-1">
                      <a href="#" class="campaigns-dropdown-item">
                        <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                        <span>Duplicate Campaign</span>
                      </a>
                      <a href="#" class="campaigns-dropdown-item">
                        <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <span>View Analytics</span>
                      </a>
                      <a href="#" class="campaigns-dropdown-item">
                        <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Export Data</span>
                      </a>
                      
                      <div class="campaigns-dropdown-divider"></div>
                      
                      <%= link_to campaign_path(campaign), method: :delete, 
                          class: "campaigns-dropdown-item campaigns-dropdown-danger",
                          data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
                        <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        <span>Delete Campaign</span>
                      <% end %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Campaign Performance Overlay (appears on hover) -->
          <div class="campaigns-performance-overlay absolute inset-0 bg-black bg-opacity-90 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center p-6">
            <div class="text-white text-center">
              <h4 class="text-lg font-bold mb-4">Quick Performance</h4>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p class="text-gray-300">Impressions</p>
                  <p class="text-2xl font-bold"><%= number_with_delimiter(campaign.impressions || 12543) %></p>
                </div>
                <div>
                  <p class="text-gray-300">Clicks</p>
                  <p class="text-2xl font-bold text-blue-400"><%= number_with_delimiter(campaign.clicks || 298) %></p>
                </div>
                <div>
                  <p class="text-gray-300">Conversions</p>
                  <p class="text-2xl font-bold text-green-400"><%= campaign.conversions || 23 %></p>
                </div>
                <div>
                  <p class="text-gray-300">Spend</p>
                  <p class="text-2xl font-bold text-yellow-400">$<%= number_with_delimiter(campaign.spend || 847) %></p>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Enhanced Table View -->
  <div class="campaigns-table-view campaign-view" data-view="table" style="display: none;">
    <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
      <!-- Table Header with Bulk Actions -->
      <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <div class="campaigns-table-bulk-selection flex items-center gap-3">
              <input type="checkbox" id="select-all-table" class="campaigns-select-all-table w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2" />
              <label for="select-all-table" class="text-sm font-medium text-gray-700">
                Select All (<span class="campaigns-selected-count">0</span>)
              </label>
            </div>
            
            <div class="campaigns-table-bulk-actions opacity-0 transition-opacity duration-200">
              <div class="flex items-center gap-2">
                <button class="campaigns-bulk-activate-table px-3 py-1.5 text-sm font-medium text-green-700 bg-green-100 rounded-lg hover:bg-green-200 transition-colors">
                  <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                  </svg>
                  Activate
                </button>
                <button class="campaigns-bulk-pause-table px-3 py-1.5 text-sm font-medium text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 transition-colors">
                  <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  Pause
                </button>
                <button class="campaigns-bulk-delete-table px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 transition-colors">
                  <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  Delete
                </button>
              </div>
            </div>
          </div>
          
          <div class="flex items-center gap-3">
            <div class="campaigns-table-search relative">
              <input type="text" placeholder="Search table..." 
                     class="campaigns-table-search-input w-64 px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white" />
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
            
            <button class="campaigns-table-export px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Export
            </button>
          </div>
        </div>
      </div>

      <!-- Enhanced Data Table -->
      <div class="campaigns-table-container overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="w-4 px-6 py-4">
                <span class="sr-only">Select</span>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="name">
                <div class="flex items-center gap-1">
                  <span>Campaign</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="type">
                <div class="flex items-center gap-1">
                  <span>Type & Channel</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="status">
                <div class="flex items-center gap-1">
                  <span>Status & Progress</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="budget">
                <div class="flex items-center gap-1">
                  <span>Budget & Spend</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="performance">
                <div class="flex items-center gap-1">
                  <span>Performance Metrics</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="campaigns-table-sort-header px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:text-gray-700 transition-colors" data-sort="dates">
                <div class="flex items-center gap-1">
                  <span>Timeline</span>
                  <svg class="w-4 h-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"></path>
                  </svg>
                </div>
              </th>
              <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <span>Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @campaigns.each_with_index do |campaign, index| %>
              <tr class="campaigns-table-row hover:bg-blue-50 transition-colors duration-200" data-campaign-id="<%= campaign.id %>">
                <!-- Selection Checkbox -->
                <td class="px-6 py-4">
                  <input type="checkbox" class="campaigns-table-select w-4 h-4 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2" 
                         value="<%= campaign.id %>" />
                </td>

                <!-- Campaign Info -->
                <td class="px-6 py-4">
                  <div class="flex items-center">
                    <div class="campaigns-table-campaign-avatar w-10 h-10 flex-shrink-0 mr-4">
                      <div class="w-10 h-10 bg-gradient-to-br 
                        <%= case campaign.campaign_type
                            when 'email' then 'from-blue-400 to-blue-600'
                            when 'social' then 'from-green-400 to-green-600'
                            when 'seo' then 'from-purple-400 to-purple-600'
                            else 'from-orange-400 to-pink-600'
                            end %> rounded-xl flex items-center justify-center">
                        <% case campaign.campaign_type %>
                        <% when 'email' %>
                          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                          </svg>
                        <% when 'social' %>
                          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                          </svg>
                        <% when 'seo' %>
                          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                          </svg>
                        <% else %>
                          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                          </svg>
                        <% end %>
                      </div>
                    </div>
                    <div class="campaigns-table-campaign-info">
                      <div class="text-sm font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                        <%= link_to campaign.name, campaign_path(campaign), class: "hover:underline" %>
                      </div>
                      <div class="text-xs text-gray-500 mt-1 max-w-xs truncate">
                        <%= truncate(campaign.description || "Advanced AI-powered campaign", length: 60) %>
                      </div>
                      <div class="text-xs text-gray-400 mt-1">
                        Target: <%= truncate(campaign.target_audience || "General Audience", length: 25) %>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Type & Channel -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-type">
                    <div class="flex items-center gap-2 mb-2">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                        <%= case campaign.campaign_type
                            when 'email' then 'bg-blue-100 text-blue-800'
                            when 'social' then 'bg-green-100 text-green-800'
                            when 'seo' then 'bg-purple-100 text-purple-800'
                            else 'bg-orange-100 text-orange-800'
                            end %>">
                        <%= case campaign.campaign_type
                            when 'email' then '📧 Email Marketing'
                            when 'social' then '📱 Social Media'
                            when 'seo' then '🔍 SEO Campaign'
                            else '⚡ Multi-Channel'
                            end %>
                      </span>
                    </div>
                    <div class="text-xs text-gray-500">
                      <%= campaign.channels&.join(', ') || 'Multiple channels' %>
                    </div>
                  </div>
                </td>

                <!-- Status & Progress -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-status">
                    <!-- Status Badge -->
                    <div class="flex items-center gap-2 mb-2">
                      <div class="w-2 h-2 rounded-full
                        <%= case campaign.status
                            when 'active' then 'bg-green-500 animate-pulse'
                            when 'paused' then 'bg-orange-500'
                            when 'draft' then 'bg-gray-400'
                            when 'completed' then 'bg-blue-500'
                            else 'bg-red-500'
                            end %>"></div>
                      <span class="text-xs font-medium
                        <%= case campaign.status
                            when 'active' then 'text-green-800'
                            when 'paused' then 'text-orange-800'
                            when 'draft' then 'text-gray-600'
                            when 'completed' then 'text-blue-800'
                            else 'text-red-800'
                            end %>">
                        <%= campaign.status.titleize %>
                      </span>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="campaigns-table-progress">
                      <div class="flex items-center justify-between text-xs text-gray-500 mb-1">
                        <span>Progress</span>
                        <span class="font-medium"><%= campaign.progress_percentage %>%</span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-1.5">
                        <div class="h-1.5 rounded-full transition-all duration-300
                          <%= case campaign.status
                              when 'active' then 'bg-green-500'
                              when 'paused' then 'bg-orange-500'
                              when 'draft' then 'bg-gray-400'
                              when 'completed' then 'bg-blue-500'
                              else 'bg-red-500'
                              end %>" 
                             style="width: <%= campaign.progress_percentage %>%"></div>
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Budget & Spend -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-budget">
                    <div class="text-sm font-semibold text-gray-900 mb-1">
                      $<%= number_with_delimiter(campaign.budget_in_dollars || 0, delimiter: ',') %>
                    </div>
                    <div class="text-xs text-gray-500 mb-2">Total Budget</div>
                    
                    <% spent = campaign.spend || (campaign.budget_in_dollars * 0.3).to_i %>
                    <div class="text-xs">
                      <div class="flex items-center justify-between mb-1">
                        <span class="text-gray-500">Spent</span>
                        <span class="font-medium text-gray-900">$<%= number_with_delimiter(spent) %></span>
                      </div>
                      <div class="w-full bg-gray-200 rounded-full h-1">
                        <% spend_percentage = ((spent.to_f / campaign.budget_in_dollars) * 100).round %>
                        <div class="h-1 bg-blue-500 rounded-full transition-all duration-300" 
                             style="width: <%= [spend_percentage, 100].min %>%"></div>
                      </div>
                      <div class="text-xs text-gray-400 mt-1">
                        <%= number_to_percentage(spend_percentage, precision: 0) %> used
                      </div>
                    </div>
                  </div>
                </td>

                <!-- Performance Metrics -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-performance space-y-2">
                    <!-- CTR -->
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">CTR</span>
                      <span class="text-xs font-semibold text-blue-600">
                        <%= number_to_percentage(campaign.click_through_rate || 2.4, precision: 1) %>
                      </span>
                    </div>
                    
                    <!-- Conversion Rate -->
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">Conv. Rate</span>

                      <span class="text-xs font-semibold text-green-600">
                        <%= number_to_percentage(campaign.conversion_rate || 4.2, precision: 1) %>
                      </span>
                    </div>
                    
                    <!-- ROI -->
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-gray-500">ROI</span>
                      <span class="text-xs font-semibold text-purple-600">
                        <%= number_to_percentage(campaign.roi || 156, precision: 0) %>
                      </span>
                    </div>
                    
                    <!-- Impressions -->
                    <div class="flex items-center justify-between pt-1 border-t border-gray-100">
                      <span class="text-xs text-gray-500">Impressions</span>
                      <span class="text-xs font-medium text-gray-900">
                        <%= number_with_delimiter(campaign.impressions || 12543) %>
                      </span>
                    </div>
                  </div>
                </td>

                <!-- Timeline -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-timeline text-xs">
                    <% if campaign.start_date %>
                      <div class="text-gray-900 font-medium mb-1">
                        <%= campaign.start_date.strftime("%b %d, %Y") %>
                      </div>
                    <% end %>
                    
                    <% if campaign.end_date %>
                      <div class="text-gray-500 mb-2">
                        to <%= campaign.end_date.strftime("%b %d, %Y") %>
                      </div>
                      
                      <% if campaign.start_date && campaign.end_date %>
                        <% days_remaining = (campaign.end_date - Date.current).to_i %>
                        <div class="campaigns-timeline-remaining
                          <%= if days_remaining > 7
                                'text-green-600'
                              elsif days_remaining > 0
                                'text-orange-600'
                              else
                                'text-red-600'
                              end %>">
                          <% if days_remaining > 0 %>
                            <%= pluralize(days_remaining, 'day') %> left
                          <% elsif days_remaining == 0 %>
                            Ends today
                          <% else %>
                            Ended <%= pluralize(days_remaining.abs, 'day') %> ago
                          <% end %>
                        </div>
                      <% end %>
                    <% else %>
                      <div class="text-gray-400">No end date</div>
                    <% end %>
                  </div>
                </td>

                <!-- Actions -->
                <td class="px-6 py-4">
                  <div class="campaigns-table-actions flex items-center gap-2">
                    <!-- Quick Action Button -->
                    <% if campaign.status == 'draft' %>
                      <%= link_to activate_campaign_path(campaign), method: :patch,
                          class: "campaigns-table-action-btn bg-green-100 text-green-700 hover:bg-green-200",
                          title: "Activate Campaign",
                          data: { confirm: "Ready to activate this campaign?" } do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                        </svg>
                      <% end %>
                    <% elsif campaign.status == 'active' %>
                      <%= link_to pause_campaign_path(campaign), method: :patch,
                          class: "campaigns-table-action-btn bg-orange-100 text-orange-700 hover:bg-orange-200",
                          title: "Pause Campaign",
                          data: { confirm: "Pause this campaign?" } do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      <% end %>
                    <% elsif campaign.status == 'paused' %>
                      <%= link_to activate_campaign_path(campaign), method: :patch,
                          class: "campaigns-table-action-btn bg-green-100 text-green-700 hover:bg-green-200",
                          title: "Resume Campaign",
                          data: { confirm: "Resume this campaign?" } do %>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                        </svg>
                      <% end %>
                    <% end %>

                    <!-- View Button -->
                    <%= link_to campaign_path(campaign),
                        class: "campaigns-table-action-btn bg-blue-100 text-blue-700 hover:bg-blue-200",
                        title: "View Campaign" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    <% end %>

                    <!-- Edit Button -->
                    <%= link_to edit_campaign_path(campaign),
                        class: "campaigns-table-action-btn bg-gray-100 text-gray-700 hover:bg-gray-200",
                        title: "Edit Campaign" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    <% end %>

                    <!-- More Actions Dropdown -->
                    <div class="campaigns-table-action-dropdown relative">
                      <button class="campaigns-table-action-btn bg-gray-100 text-gray-700 hover:bg-gray-200" 
                              data-dropdown-toggle="table-campaign-<%= campaign.id %>-menu" title="More Actions">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                        </svg>
                      </button>
                      
                      <div class="campaigns-table-dropdown-menu" id="table-campaign-<%= campaign.id %>-menu">
                        <div class="py-1">
                          <a href="#" class="campaigns-table-dropdown-item">
                            <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            <span>Duplicate</span>
                          </a>
                          <a href="#" class="campaigns-table-dropdown-item">
                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                            <span>Analytics</span>
                          </a>
                          <a href="#" class="campaigns-table-dropdown-item">
                            <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            <span>Export Data</span>
                          </a>
                          
                          <div class="campaigns-table-dropdown-divider"></div>
                          
                          <%= link_to campaign_path(campaign), method: :delete,
                              class: "campaigns-table-dropdown-item campaigns-table-dropdown-danger",
                              data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
                            <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                            <span>Delete</span>
                          <% end %>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Table Footer with Statistics -->
      <div class="campaigns-table-footer px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-6 text-sm text-gray-600">
            <span>Showing <%= @campaigns.count %> campaigns</span>
            <span>Total Budget: $<%= number_with_delimiter(@campaigns.sum(:budget_cents) / 100) %></span>
            <span>Active: <%= @campaigns.where(status: 'active').count %></span>
          </div>
          
          <div class="flex items-center gap-2">
            <span class="text-sm text-gray-500">Rows per page:</span>
            <select class="campaigns-table-page-size text-sm border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="10">10</option>
              <option value="25" selected>25</option>
              <option value="50">50</option>
              <option value="100">100</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Mobile Card View -->
  <div class="campaigns-mobile-view">
    <div class="campaigns-mobile-cards space-y-4">
      <% @campaigns.each_with_index do |campaign, index| %>
        <div class="campaigns-mobile-card bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden" 
             data-campaign-id="<%= campaign.id %>" 
             style="animation-delay: <%= index * 0.1 %>s">
          
          <!-- Mobile Card Header -->
          <div class="campaigns-mobile-header relative">
            <!-- Background Gradient -->
            <div class="absolute inset-0 bg-gradient-to-br 
              <%= case campaign.campaign_type
                  when 'email' then 'from-blue-400 to-blue-600'
                  when 'social' then 'from-green-400 to-green-600'
                  when 'seo' then 'from-purple-400 to-purple-600'
                  else 'from-orange-400 to-pink-600'
                  end %> opacity-10"></div>
            
            <div class="relative flex items-start justify-between p-4">
              <!-- Campaign Type & Status -->
              <div class="flex items-center gap-3">
                <div class="campaigns-mobile-type-icon w-12 h-12 bg-gradient-to-br 
                  <%= case campaign.campaign_type
                      when 'email' then 'from-blue-400 to-blue-600'
                      when 'social' then 'from-green-400 to-green-600'
                      when 'seo' then 'from-purple-400 to-purple-600'
                      else 'from-orange-400 to-pink-600'
                      end %> rounded-xl flex items-center justify-center shadow-lg">
                  <% case campaign.campaign_type %>
                  <% when 'email' %>
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                    </svg>
                  <% when 'social' %>
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
                    </svg>
                  <% when 'seo' %>
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                  <% else %>
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  <% end %>
                </div>
                
                <div>
                  <div class="text-xs font-medium text-gray-600 mb-1">
                    <%= case campaign.campaign_type
                        when 'email' then '📧 Email Marketing'
                        when 'social' then '📱 Social Media'
                        when 'seo' then '🔍 SEO Campaign'
                        else '⚡ Multi-Channel'
                        end %>
                  </div>
                  <div class="campaigns-mobile-status-badge
                    <%= case campaign.status
                        when 'active' then 'text-green-800 bg-green-100'
                        when 'paused' then 'text-orange-800 bg-orange-100'
                        when 'draft' then 'text-gray-600 bg-gray-100'
                        when 'completed' then 'text-blue-800 bg-blue-100'
                        else 'text-red-800 bg-red-100'
                        end %> inline-flex items-center gap-2 px-2.5 py-1 rounded-full text-xs font-medium">
                    <div class="w-2 h-2 rounded-full
                      <%= case campaign.status
                          when 'active' then 'bg-green-500 animate-pulse'
                          when 'paused' then 'bg-orange-500'
                          when 'draft' then 'bg-gray-400'
                          when 'completed' then 'bg-blue-500'
                          else 'bg-red-500'
                          end %>"></div>
                    <%= campaign.status.titleize %>
                  </div>
                </div>
              </div>

              <!-- Mobile Selection Checkbox -->
              <div class="campaigns-mobile-selection">
                <input type="checkbox" class="campaigns-mobile-select w-5 h-5 text-blue-600 bg-white border-gray-300 rounded focus:ring-blue-500 focus:ring-2" 
                       value="<%= campaign.id %>" />
              </div>
            </div>
          </div>

          <!-- Mobile Card Content -->
          <div class="campaigns-mobile-content p-4">
            <!-- Campaign Title -->
            <%= link_to campaign_path(campaign), class: "campaigns-mobile-title-link block mb-3" do %>
              <h3 class="text-lg font-bold text-gray-900 hover:text-blue-600 transition-colors leading-tight">
                <%= truncate(campaign.name, length: 60) %>
              </h3>
            <% end %>
            
            <!-- Campaign Description -->
            <p class="text-sm text-gray-600 mb-4 leading-relaxed">
              <%= truncate(campaign.description || "AI-powered marketing campaign with advanced targeting and optimization", length: 100) %>
            </p>

            <!-- Mobile Stats Grid -->
            <div class="campaigns-mobile-stats grid grid-cols-2 gap-4 mb-4">
              <!-- Budget -->
              <div class="campaigns-mobile-stat bg-gray-50 rounded-xl p-3">
                <div class="flex items-center gap-2 mb-1">
                  <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                  </svg>
                  <span class="text-xs text-gray-500 font-medium">Budget</span>
                </div>
                <p class="text-lg font-bold text-green-600">
                  $<%= number_with_delimiter(campaign.budget_in_dollars || 0) %>
                </p>
                <% spent = campaign.spend || (campaign.budget_in_dollars * 0.3).to_i %>
                <p class="text-xs text-gray-500 mt-1">
                  $<%= number_with_delimiter(spent) %> spent
                </p>
              </div>

              <!-- Target Audience -->
              <div class="campaigns-mobile-stat bg-gray-50 rounded-xl p-3">
                <div class="flex items-center gap-2 mb-1">
                  <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                  <span class="text-xs text-gray-500 font-medium">Audience</span>
                </div>
                <p class="text-sm font-semibold text-gray-900 leading-tight">
                  <%= truncate(campaign.target_audience || "General Audience", length: 20) %>
                </p>
                <p class="text-xs text-gray-500 mt-1">Target market</p>
              </div>
            </div>

            <!-- Mobile Performance Metrics -->
            <div class="campaigns-mobile-performance bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-4 mb-4">
              <h4 class="text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
                Performance Metrics
              </h4>
              <div class="grid grid-cols-3 gap-3">
                <div class="text-center">
                  <p class="text-xs text-gray-500 mb-1">CTR</p>
                  <p class="text-sm font-bold text-blue-600">
                    <%= number_to_percentage(campaign.click_through_rate || 2.4, precision: 1) %>
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-xs text-gray-500 mb-1">Conv. Rate</p>
                  <p class="text-sm font-bold text-green-600">
                    <%= number_to_percentage(campaign.conversion_rate || 4.2, precision: 1) %>
                  </p>
                </div>
                <div class="text-center">
                  <p class="text-xs text-gray-500 mb-1">ROI</p>
                  <p class="text-sm font-bold text-purple-600">
                    <%= number_to_percentage(campaign.roi || 156, precision: 0) %>
                  </p>
                </div>
              </div>
            </div>

            <!-- Mobile Progress Section -->
            <div class="campaigns-mobile-progress mb-4">
              <div class="flex items-center justify-between text-sm mb-2">
                <span class="text-gray-600 font-medium">Campaign Progress</span>
                <span class="font-bold text-gray-900"><%= campaign.progress_percentage %>%</span>
              </div>
              <div class="relative">
                <div class="campaigns-mobile-progress-bar bg-gray-200 rounded-full h-3 overflow-hidden">
                  <div class="campaigns-mobile-progress-fill absolute left-0 top-0 h-full rounded-full transition-all duration-700 ease-out
                    <%= case campaign.status
                        when 'active' then 'bg-gradient-to-r from-green-400 to-green-600'
                        when 'paused' then 'bg-gradient-to-r from-orange-400 to-orange-600'
                        when 'draft' then 'bg-gradient-to-r from-gray-400 to-gray-600'
                        when 'completed' then 'bg-gradient-to-r from-blue-400 to-blue-600'
                        else 'bg-gradient-to-r from-red-400 to-red-600'
                        end %>" 
                       style="width: <%= campaign.progress_percentage %>%">
                    <div class="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile Timeline -->
            <% if campaign.start_date || campaign.end_date %>
              <div class="campaigns-mobile-timeline bg-blue-50 rounded-xl p-3 mb-4">
                <div class="flex items-center gap-2 text-sm">
                  <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                  <div class="flex-1">
                    <% if campaign.start_date && campaign.end_date %>
                      <p class="font-medium text-blue-800">
                        <%= campaign.start_date.strftime("%b %d") %> - <%= campaign.end_date.strftime("%b %d, %Y") %>
                      </p>
                      <% days_remaining = (campaign.end_date - Date.current).to_i %>
                      <p class="text-xs text-blue-600 mt-1">
                        <% if days_remaining > 0 %>
                          <%= pluralize(days_remaining, 'day') %> remaining
                        <% elsif days_remaining == 0 %>
                          Last day!
                        <% else %>
                          Ended <%= pluralize(days_remaining.abs, 'day') %> ago
                        <% end %>
                      </p>
                    <% elsif campaign.start_date %>
                      <p class="font-medium text-blue-800">Started <%= campaign.start_date.strftime("%b %d, %Y") %></p>
                      <p class="text-xs text-blue-600 mt-1">No end date set</p>
                    <% elsif campaign.end_date %>
                      <p class="font-medium text-blue-800">Ends <%= campaign.end_date.strftime("%b %d, %Y") %></p>
                      <p class="text-xs text-blue-600 mt-1">No start date set</p>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>
          </div>

          <!-- Mobile Card Actions -->
          <div class="campaigns-mobile-actions border-t border-gray-100 p-4 bg-gray-50">
            <div class="flex items-center justify-between gap-3">
              <!-- Primary Actions -->
              <div class="flex items-center gap-2 flex-1">
                <%= link_to campaign_path(campaign), 
                    class: "campaigns-mobile-action-btn campaigns-mobile-action-view flex-1" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  <span>View</span>
                <% end %>
                
                <%= link_to edit_campaign_path(campaign), 
                    class: "campaigns-mobile-action-btn campaigns-mobile-action-edit flex-1" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                  </svg>
                  <span>Edit</span>
                <% end %>
              </div>

              <!-- Status Action Button -->
              <div class="campaigns-mobile-status-actions">
                <% if campaign.status == 'draft' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "campaigns-mobile-status-btn bg-green-500 hover:bg-green-600 text-white active:bg-green-700",
                      data: { confirm: "Ready to activate this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    <span>Activate</span>
                  <% end %>
                <% elsif campaign.status == 'active' %>
                  <%= link_to pause_campaign_path(campaign), method: :patch, 
                      class: "campaigns-mobile-status-btn bg-orange-500 hover:bg-orange-600 text-white active:bg-orange-700",
                      data: { confirm: "Pause this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Pause</span>
                  <% end %>
                <% elsif campaign.status == 'paused' %>
                  <%= link_to activate_campaign_path(campaign), method: :patch, 
                      class: "campaigns-mobile-status-btn bg-green-500 hover:bg-green-600 text-white active:bg-green-700",
                      data: { confirm: "Resume this campaign?" } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m2-10.586V18a2 2 0 01-2 2H6a2 2 0 01-2-2V7.414a1 1 0 01.293-.707l2-2A1 1 0 017 4h4.586a1 1 0 01.707.293l2 2A1 1 0 0115 7.414z"></path>
                    </svg>
                    <span>Resume</span>
                  <% end %>
                <% end %>
              </div>

              <!-- More Actions -->
              <div class="campaigns-mobile-more-actions">
                <button class="campaigns-mobile-more-btn" data-dropdown-toggle="mobile-campaign-<%= campaign.id %>-menu">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                
                <div class="campaigns-mobile-dropdown-menu" id="mobile-campaign-<%= campaign.id %>-menu">
                  <div class="py-1">
                    <a href="#" class="campaigns-mobile-dropdown-item">
                      <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                      </svg>
                      <span>Duplicate Campaign</span>
                    </a>
                    <a href="#" class="campaigns-mobile-dropdown-item">
                      <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                      <span>View Analytics</span>
                    </a>
                    <a href="#" class="campaigns-mobile-dropdown-item">
                      <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                      <span>Export Data</span>
                    </a>
                    
                    <div class="campaigns-mobile-dropdown-divider"></div>
                    
                    <%= link_to campaign_path(campaign), method: :delete, 
                        class: "campaigns-mobile-dropdown-item campaigns-mobile-dropdown-danger",
                        data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
                      <svg class="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                      </svg>
                      <span>Delete Campaign</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Mobile Swipe Actions Indicator -->
          <div class="campaigns-mobile-swipe-indicator absolute bottom-2 right-2 opacity-30">
            <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
            </svg>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Mobile Bulk Actions Bar (appears when items are selected) -->
    <div class="campaigns-mobile-bulk-bar fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 p-4 transform translate-y-full transition-transform duration-300 z-50">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <span class="text-sm font-medium text-gray-700">
            <span class="campaigns-mobile-selected-count">0</span> selected
          </span>
        </div>
        
        <div class="flex items-center gap-2">
          <button class="campaigns-mobile-bulk-activate px-4 py-2 text-sm font-medium text-green-700 bg-green-100 rounded-lg hover:bg-green-200 active:bg-green-300 transition-colors">
            Activate
          </button>
          <button class="campaigns-mobile-bulk-pause px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 active:bg-orange-300 transition-colors">
            Pause
          </button>
          <button class="campaigns-mobile-bulk-delete px-4 py-2 text-sm font-medium text-red-700 bg-red-100 rounded-lg hover:bg-red-200 active:bg-red-300 transition-colors">
            Delete
          </button>
          <button class="campaigns-mobile-bulk-cancel px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 active:bg-gray-300 transition-colors">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Mobile Card View -->
  <div class="campaigns-mobile-view campaign-view md:hidden" data-view="mobile" style="display: none;">
    <div class="space-y-4">
      <% @campaigns.each do |campaign| %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <!-- Campaign Header -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">
                <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600" %>
              </h3>
              <p class="text-sm text-gray-600 mb-2"><%= campaign.description %></p>
              <p class="text-xs text-gray-500">Target: <%= campaign.target_audience %></p>
            </div>

          <!-- Status Badge -->
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-4
            <%= case campaign.status
                when 'active' then 'bg-green-100 text-green-800'
                when 'draft' then 'bg-gray-100 text-gray-800'
                when 'paused' then 'bg-yellow-100 text-yellow-800'
                when 'completed' then 'bg-blue-100 text-blue-800'
                when 'cancelled' then 'bg-red-100 text-red-800'
                else 'bg-gray-100 text-gray-800'
                end %>">
            <%= campaign.status.titleize %>
          </span>
        </div>

        <!-- Campaign Details -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <!-- Type -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Type</p>
            <div class="flex items-center">
              <% case campaign.campaign_type %>
              <% when 'email' %>
                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-blue-600 font-bold text-xs">@</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Email</span>
              <% when 'social' %>
                <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-green-600 font-bold text-xs">#</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Social</span>
              <% when 'seo' %>
                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-purple-600 font-bold text-xs">S</span>
                </div>
                <span class="text-sm font-medium text-gray-900">SEO</span>
              <% when 'multi_channel' %>
                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-orange-600 font-bold text-xs">⚡</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Multi-Channel</span>
              <% end %>
            </div>
          </div>

          <!-- Budget -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Budget</p>
            <p class="text-sm font-medium text-gray-900">
              $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
            </p>
          </div>
        </div>

        <!-- Dates -->
        <div class="mb-4">
          <p class="text-xs text-gray-500 mb-1">Duration</p>
          <div class="text-sm text-gray-900">
            <% if campaign.start_date %>
              <%= campaign.start_date.strftime("%b %d, %Y") %>
              <% if campaign.end_date %>
                - <%= campaign.end_date.strftime("%b %d, %Y") %>
              <% end %>
            <% else %>
              <span class="text-gray-400">Dates not set</span>
            <% end %>
          </div>
        </div>

        <!-- Progress -->
        <div class="mb-4">
          <div class="flex items-center justify-between text-sm mb-2">
            <span class="text-gray-600">Progress</span>
            <span class="font-medium"><%= campaign.progress_percentage %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 style="width: <%= campaign.progress_percentage %>%"></div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-3">
            <%= link_to campaign_path(campaign),
                class: "text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors",
                title: "View Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            <% end %>

            <%= link_to edit_campaign_path(campaign),
                class: "text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors",
                title: "Edit Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            <% end %>
          </div>

          <div class="flex items-center space-x-2">
            <!-- Status Action Button -->
            <% if campaign.can_be_activated? %>
              <%= link_to activate_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full hover:bg-green-200 transition-colors",
                  data: { confirm: "Are you sure you want to activate this campaign?" } do %>
                Activate
              <% end %>
            <% elsif campaign.active? %>
              <%= link_to pause_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full hover:bg-yellow-200 transition-colors",
                  data: { confirm: "Are you sure you want to pause this campaign?" } do %>
                Pause
              <% end %>
            <% end %>

            <%= link_to campaign_path(campaign), method: :delete,
                class: "text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors",
                title: "Delete Campaign",
                data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
    </div>
  </div>

  </div>
<% else %>
  <!-- Enhanced Empty State -->
  <div class="campaigns-empty-state">
    <div class="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 rounded-2xl shadow-lg border border-white/20 backdrop-blur-sm p-12 text-center">
      <div class="max-w-2xl mx-auto">
        <% if params[:search].present? || params[:status].present? || params[:type].present? %>
          <!-- Filtered Empty State -->
          <div class="campaigns-empty-icon w-32 h-32 bg-gradient-to-br from-orange-100 to-red-200 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-16 h-16 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"></path>
            </svg>
          </div>
          
          <h3 class="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-4">
            No Results Found
          </h3>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            No campaigns match your current filters. Try adjusting your search criteria or clearing filters to see all campaigns.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to campaigns_path,
                class: "campaigns-clear-filters-btn inline-flex items-center px-8 py-4 bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Clear All Filters
            <% end %>
            
            <% secondary_btn_classes = "campaigns-create-secondary-btn inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300" %>
            <%= link_to new_campaign_path, class: secondary_btn_classes do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Create New Campaign
            <% end %>
          </div>
          
        <% else %>
          <!-- First Time Empty State with Quick Start Guide -->
          <div class="campaigns-empty-icon w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg class="w-16 h-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          
          <h3 class="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent mb-4">
            Welcome to Campaign Center
          </h3>
          <p class="text-xl text-gray-600 mb-12 leading-relaxed">
            Launch powerful AI-driven marketing campaigns that convert prospects into customers with precision and intelligence.
          </p>
          
          <!-- Quick Start Guide -->
          <div class="campaigns-quick-start-grid grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 text-left">
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">1. Create Campaign</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Set up your campaign goals, target audience, and budget parameters using our AI-powered recommendations.</p>
            </div>
            
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">2. Launch & Monitor</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Deploy your campaign across multiple channels and track real-time performance with advanced analytics.</p>
            </div>
            
            <div class="campaigns-quick-start-card bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/40 hover:shadow-lg transition-all duration-300">
              <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mb-4">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <h4 class="text-lg font-semibold text-gray-900 mb-2">3. Optimize Results</h4>
              <p class="text-gray-600 text-sm leading-relaxed">Use AI insights to continuously improve performance and maximize your return on investment.</p>
            </div>
          </div>
          
          <!-- Primary CTA -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <% primary_btn_classes = "campaigns-create-primary-btn group inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300" %>
            <%= link_to new_campaign_path, class: primary_btn_classes do %>
              <svg class="w-6 h-6 mr-3 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              <span>Create Your First Campaign</span>
              <div class="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </div>
            <% end %>
            
            <button class="campaigns-tour-btn inline-flex items-center px-8 py-4 bg-white text-gray-700 font-semibold rounded-xl shadow-md hover:shadow-lg border border-gray-200 transform hover:-translate-y-1 transition-all duration-300">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Take a Quick Tour
            </button>
          </div>
          
        <% end %>
      </div>
    </div>
  </div>
<% end %>

<!-- Pagination -->
<% if defined?(@pagy) && @pagy.pages > 1 %>
  <div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
      <!-- Previous Page -->
      <% if @pagy.prev %>
        <% pagination_link_classes = "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" %>
        <% prev_params = { page: @pagy.prev, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort] } %>
        <%= link_to campaigns_path(prev_params), class: pagination_link_classes do %>
          Previous
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Previous
        </span>
      <% end %>

      <!-- Page Numbers -->
      <% @pagy.series.each do |item| %>
        <% if item.is_a?(Integer) %>
          <% if item == @pagy.page %>
            <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
              <%= item %>
            </span>
          <% else %>
            <% page_params = { page: item, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort] } %>
            <%= link_to campaigns_path(page_params), class: pagination_link_classes do %>
              <%= item %>
            <% end %>
          <% end %>
        <% elsif item == :gap %>
          <span class="px-3 py-2 text-sm font-medium text-gray-300">...</span>
        <% end %>
      <% end %>

      <!-- Next Page -->
      <% if @pagy.next %>
        <% next_params = { page: @pagy.next, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort] } %>
        <%= link_to campaigns_path(next_params), class: pagination_link_classes do %>
          Next
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Next
        </span>
      <% end %>
    </nav>
  </div>
<% end %>
